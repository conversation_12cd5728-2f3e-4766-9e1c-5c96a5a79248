import * as React from "react"
import { motion } from "framer-motion"
import { cn } from "@/lib/utils"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"

interface EnhancedAvatarProps {
  src?: string
  alt?: string
  fallback?: string
  size?: "sm" | "md" | "lg" | "xl" | "2xl"
  status?: "online" | "offline" | "away" | "busy"
  showStatus?: boolean
  className?: string
  ring?: boolean
  ringColor?: string
  children?: React.ReactNode
}

const EnhancedAvatar = React.forwardRef<HTMLDivElement, EnhancedAvatarProps>(
  ({ 
    src, 
    alt, 
    fallback, 
    size = "md", 
    status, 
    showStatus = false, 
    className, 
    ring = false,
    ringColor = "ring-primary/20",
    children,
    ...props 
  }, ref) => {
    const sizeClasses = {
      sm: "h-8 w-8",
      md: "h-10 w-10",
      lg: "h-12 w-12",
      xl: "h-16 w-16",
      "2xl": "h-20 w-20"
    }

    const statusColors = {
      online: "bg-green-500",
      offline: "bg-gray-400",
      away: "bg-yellow-500",
      busy: "bg-red-500"
    }

    const statusSizes = {
      sm: "h-2 w-2",
      md: "h-2.5 w-2.5",
      lg: "h-3 w-3",
      xl: "h-3.5 w-3.5",
      "2xl": "h-4 w-4"
    }

    return (
      <motion.div
        ref={ref}
        className={cn("relative inline-block", className)}
        whileHover={{ scale: 1.05 }}
        transition={{ duration: 0.2 }}
        {...props}
      >
        <Avatar 
          className={cn(
            sizeClasses[size],
            ring && `ring-2 ${ringColor}`,
            "transition-all duration-200"
          )}
        >
          <AvatarImage src={src} alt={alt} />
          <AvatarFallback className="bg-gradient-to-br from-primary/20 to-primary/10 text-primary font-medium">
            {fallback}
          </AvatarFallback>
        </Avatar>
        
        {showStatus && status && (
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            className={cn(
              "absolute -bottom-0.5 -right-0.5 rounded-full border-2 border-background",
              statusColors[status],
              statusSizes[size]
            )}
          />
        )}
        
        {children}
      </motion.div>
    )
  }
)
EnhancedAvatar.displayName = "EnhancedAvatar"

interface AvatarGroupProps {
  avatars: Array<{
    src?: string
    alt?: string
    fallback?: string
  }>
  max?: number
  size?: "sm" | "md" | "lg"
  className?: string
}

const AvatarGroup = React.forwardRef<HTMLDivElement, AvatarGroupProps>(
  ({ avatars, max = 3, size = "md", className, ...props }, ref) => {
    const displayAvatars = avatars.slice(0, max)
    const remainingCount = avatars.length - max

    const sizeClasses = {
      sm: "h-8 w-8",
      md: "h-10 w-10",
      lg: "h-12 w-12"
    }

    return (
      <div
        ref={ref}
        className={cn("flex -space-x-2", className)}
        {...props}
      >
        {displayAvatars.map((avatar, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, x: -10 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <Avatar className={cn(sizeClasses[size], "border-2 border-background")}>
              <AvatarImage src={avatar.src} alt={avatar.alt} />
              <AvatarFallback className="bg-gradient-to-br from-primary/20 to-primary/10 text-primary font-medium text-xs">
                {avatar.fallback}
              </AvatarFallback>
            </Avatar>
          </motion.div>
        ))}
        
        {remainingCount > 0 && (
          <motion.div
            initial={{ opacity: 0, x: -10 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: displayAvatars.length * 0.1 }}
          >
            <Avatar className={cn(sizeClasses[size], "border-2 border-background")}>
              <AvatarFallback className="bg-gradient-to-br from-muted to-muted/80 text-muted-foreground font-medium text-xs">
                +{remainingCount}
              </AvatarFallback>
            </Avatar>
          </motion.div>
        )}
      </div>
    )
  }
)
AvatarGroup.displayName = "AvatarGroup"

export { EnhancedAvatar, AvatarGroup }
