import * as React from "react"
import { motion } from "framer-motion"
import { cn } from "@/lib/utils"
import { EnhancedCard, EnhancedCardContent } from "./enhanced-card"

interface StatsCardProps {
  title: string
  value: string | number
  subtitle?: string
  icon?: React.ReactNode
  trend?: {
    value: number
    label: string
    isPositive?: boolean
  }
  variant?: "default" | "primary" | "success" | "warning" | "danger"
  className?: string
}

const StatsCard = React.forwardRef<HTMLDivElement, StatsCardProps>(
  ({ title, value, subtitle, icon, trend, variant = "default", className, ...props }, ref) => {
    const variants = {
      default: "border-border/40",
      primary: "border-primary/20 bg-gradient-to-br from-primary/5 to-primary/10",
      success: "border-green-200 bg-gradient-to-br from-green-50 to-green-100 dark:border-green-800 dark:from-green-950 dark:to-green-900",
      warning: "border-yellow-200 bg-gradient-to-br from-yellow-50 to-yellow-100 dark:border-yellow-800 dark:from-yellow-950 dark:to-yellow-900",
      danger: "border-red-200 bg-gradient-to-br from-red-50 to-red-100 dark:border-red-800 dark:from-red-950 dark:to-red-900"
    }

    const iconColors = {
      default: "text-muted-foreground",
      primary: "text-primary",
      success: "text-green-600 dark:text-green-400",
      warning: "text-yellow-600 dark:text-yellow-400",
      danger: "text-red-600 dark:text-red-400"
    }

    return (
      <EnhancedCard
        ref={ref}
        variant="elevated"
        className={cn(variants[variant], className)}
        {...props}
      >
        <EnhancedCardContent className="p-6">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                {icon && (
                  <div className={cn("flex-shrink-0", iconColors[variant])}>
                    {icon}
                  </div>
                )}
                <p className="text-sm font-medium text-muted-foreground leading-none">
                  {title}
                </p>
              </div>
              
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
              >
                <p className="text-2xl font-bold tracking-tight mb-1">
                  {value}
                </p>
              </motion.div>
              
              {subtitle && (
                <p className="text-xs text-muted-foreground">
                  {subtitle}
                </p>
              )}
            </div>
          </div>
          
          {trend && (
            <motion.div
              initial={{ opacity: 0, x: -10 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3, delay: 0.1 }}
              className="flex items-center gap-1 mt-3 pt-3 border-t border-border/40"
            >
              <div className={cn(
                "flex items-center gap-1 text-xs font-medium",
                trend.isPositive !== false ? "text-green-600 dark:text-green-400" : "text-red-600 dark:text-red-400"
              )}>
                <span className={cn(
                  "inline-block w-0 h-0 border-l-[3px] border-r-[3px] border-l-transparent border-r-transparent",
                  trend.isPositive !== false 
                    ? "border-b-[4px] border-b-green-600 dark:border-b-green-400" 
                    : "border-t-[4px] border-t-red-600 dark:border-t-red-400"
                )} />
                {Math.abs(trend.value)}%
              </div>
              <span className="text-xs text-muted-foreground">
                {trend.label}
              </span>
            </motion.div>
          )}
        </EnhancedCardContent>
      </EnhancedCard>
    )
  }
)
StatsCard.displayName = "StatsCard"

interface StatsGridProps {
  children: React.ReactNode
  columns?: 1 | 2 | 3 | 4
  className?: string
}

const StatsGrid = React.forwardRef<HTMLDivElement, StatsGridProps>(
  ({ children, columns = 4, className, ...props }, ref) => {
    const gridCols = {
      1: "grid-cols-1",
      2: "grid-cols-1 md:grid-cols-2",
      3: "grid-cols-1 md:grid-cols-2 lg:grid-cols-3",
      4: "grid-cols-1 md:grid-cols-2 lg:grid-cols-4"
    }

    return (
      <div
        ref={ref}
        className={cn(
          "grid gap-4",
          gridCols[columns],
          className
        )}
        {...props}
      >
        {children}
      </div>
    )
  }
)
StatsGrid.displayName = "StatsGrid"

export { StatsCard, StatsGrid }
