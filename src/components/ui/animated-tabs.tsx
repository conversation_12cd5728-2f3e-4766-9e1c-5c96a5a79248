import * as React from "react"
import { motion, AnimatePresence } from "framer-motion"
import { cn } from "@/lib/utils"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"

interface AnimatedTabsProps {
  defaultValue: string
  children: React.ReactNode
  className?: string
  onValueChange?: (value: string) => void
  value?: string
}

const AnimatedTabs = React.forwardRef<HTMLDivElement, AnimatedTabsProps>(
  ({ defaultValue, children, className, onValueChange, value, ...props }, ref) => {
    return (
      <Tabs
        ref={ref}
        defaultValue={defaultValue}
        value={value}
        onValueChange={onValueChange}
        className={cn("w-full", className)}
        {...props}
      >
        {children}
      </Tabs>
    )
  }
)
AnimatedTabs.displayName = "AnimatedTabs"

interface AnimatedTabsListProps {
  children: React.ReactNode
  className?: string
}

const AnimatedTabsList = React.forwardRef<HTMLDivElement, AnimatedTabsListProps>(
  ({ children, className, ...props }, ref) => {
    return (
      <TabsList
        ref={ref}
        className={cn(
          "bg-card/60 backdrop-blur-sm border border-border/40 p-1 rounded-xl shadow-sm",
          className
        )}
        {...props}
      >
        {children}
      </TabsList>
    )
  }
)
AnimatedTabsList.displayName = "AnimatedTabsList"

interface AnimatedTabsTriggerProps {
  value: string
  children: React.ReactNode
  className?: string
  icon?: React.ReactNode
}

const AnimatedTabsTrigger = React.forwardRef<HTMLButtonElement, AnimatedTabsTriggerProps>(
  ({ value, children, className, icon, ...props }, ref) => {
    return (
      <TabsTrigger
        ref={ref}
        value={value}
        className={cn(
          "relative px-4 py-2 text-sm rounded-lg transition-all duration-200",
          "data-[state=active]:bg-primary data-[state=active]:text-primary-foreground",
          "data-[state=active]:shadow-sm hover:bg-muted/80",
          "group overflow-hidden",
          className
        )}
        {...props}
      >
        <motion.div
          className="flex items-center gap-2 relative z-10"
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          {icon && (
            <motion.div
              initial={{ rotate: 0 }}
              whileHover={{ rotate: 5 }}
              transition={{ duration: 0.2 }}
            >
              {icon}
            </motion.div>
          )}
          {children}
        </motion.div>
        
        {/* Animated background for active state */}
        <motion.div
          className="absolute inset-0 bg-gradient-to-r from-primary/10 to-primary/5 rounded-lg"
          initial={{ opacity: 0, scale: 0.8 }}
          whileHover={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.2 }}
        />
      </TabsTrigger>
    )
  }
)
AnimatedTabsTrigger.displayName = "AnimatedTabsTrigger"

interface AnimatedTabsContentProps {
  value: string
  children: React.ReactNode
  className?: string
}

const AnimatedTabsContent = React.forwardRef<HTMLDivElement, AnimatedTabsContentProps>(
  ({ value, children, className, ...props }, ref) => {
    return (
      <TabsContent
        ref={ref}
        value={value}
        className={cn("mt-6", className)}
        {...props}
      >
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.3, ease: "easeOut" }}
        >
          {children}
        </motion.div>
      </TabsContent>
    )
  }
)
AnimatedTabsContent.displayName = "AnimatedTabsContent"

export {
  AnimatedTabs,
  AnimatedTabsList,
  AnimatedTabsTrigger,
  AnimatedTabsContent,
}
