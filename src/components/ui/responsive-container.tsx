import * as React from "react"
import { motion } from "framer-motion"
import { cn } from "@/lib/utils"

interface ResponsiveContainerProps {
  children: React.ReactNode
  className?: string
  maxWidth?: "sm" | "md" | "lg" | "xl" | "2xl" | "full"
  padding?: "none" | "sm" | "md" | "lg"
  center?: boolean
}

const ResponsiveContainer = React.forwardRef<HTMLDivElement, ResponsiveContainerProps>(
  ({ children, className, maxWidth = "lg", padding = "md", center = true, ...props }, ref) => {
    const maxWidthClasses = {
      sm: "max-w-sm",
      md: "max-w-md", 
      lg: "max-w-4xl",
      xl: "max-w-6xl",
      "2xl": "max-w-7xl",
      full: "max-w-full"
    }

    const paddingClasses = {
      none: "",
      sm: "px-3 py-4 sm:px-4 sm:py-6",
      md: "px-4 py-6 sm:px-6 sm:py-8 md:px-8 md:py-10",
      lg: "px-6 py-8 sm:px-8 sm:py-10 md:px-12 md:py-16"
    }

    return (
      <div
        ref={ref}
        className={cn(
          "w-full",
          center && "mx-auto",
          maxWidthClasses[maxWidth],
          paddingClasses[padding],
          className
        )}
        {...props}
      >
        {children}
      </div>
    )
  }
)
ResponsiveContainer.displayName = "ResponsiveContainer"

interface ResponsiveGridProps {
  children: React.ReactNode
  className?: string
  columns?: {
    default: number
    sm?: number
    md?: number
    lg?: number
    xl?: number
  }
  gap?: "sm" | "md" | "lg"
}

const ResponsiveGrid = React.forwardRef<HTMLDivElement, ResponsiveGridProps>(
  ({ children, className, columns = { default: 1 }, gap = "md", ...props }, ref) => {
    const gapClasses = {
      sm: "gap-3",
      md: "gap-4 sm:gap-6",
      lg: "gap-6 sm:gap-8"
    }

    const getGridCols = () => {
      const { default: defaultCols, sm, md, lg, xl } = columns
      let classes = `grid-cols-${defaultCols}`
      
      if (sm) classes += ` sm:grid-cols-${sm}`
      if (md) classes += ` md:grid-cols-${md}`
      if (lg) classes += ` lg:grid-cols-${lg}`
      if (xl) classes += ` xl:grid-cols-${xl}`
      
      return classes
    }

    return (
      <div
        ref={ref}
        className={cn(
          "grid",
          getGridCols(),
          gapClasses[gap],
          className
        )}
        {...props}
      >
        {children}
      </div>
    )
  }
)
ResponsiveGrid.displayName = "ResponsiveGrid"

interface ResponsiveStackProps {
  children: React.ReactNode
  className?: string
  direction?: "vertical" | "horizontal" | "responsive"
  gap?: "sm" | "md" | "lg"
  align?: "start" | "center" | "end" | "stretch"
  justify?: "start" | "center" | "end" | "between" | "around"
}

const ResponsiveStack = React.forwardRef<HTMLDivElement, ResponsiveStackProps>(
  ({ 
    children, 
    className, 
    direction = "vertical", 
    gap = "md", 
    align = "stretch",
    justify = "start",
    ...props 
  }, ref) => {
    const gapClasses = {
      sm: "gap-2",
      md: "gap-4",
      lg: "gap-6"
    }

    const alignClasses = {
      start: "items-start",
      center: "items-center", 
      end: "items-end",
      stretch: "items-stretch"
    }

    const justifyClasses = {
      start: "justify-start",
      center: "justify-center",
      end: "justify-end", 
      between: "justify-between",
      around: "justify-around"
    }

    const directionClasses = {
      vertical: "flex-col",
      horizontal: "flex-row",
      responsive: "flex-col sm:flex-row"
    }

    return (
      <div
        ref={ref}
        className={cn(
          "flex",
          directionClasses[direction],
          gapClasses[gap],
          alignClasses[align],
          justifyClasses[justify],
          className
        )}
        {...props}
      >
        {children}
      </div>
    )
  }
)
ResponsiveStack.displayName = "ResponsiveStack"

interface MobileOnlyProps {
  children: React.ReactNode
  className?: string
}

const MobileOnly = React.forwardRef<HTMLDivElement, MobileOnlyProps>(
  ({ children, className, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn("block sm:hidden", className)}
        {...props}
      >
        {children}
      </div>
    )
  }
)
MobileOnly.displayName = "MobileOnly"

interface DesktopOnlyProps {
  children: React.ReactNode
  className?: string
}

const DesktopOnly = React.forwardRef<HTMLDivElement, DesktopOnlyProps>(
  ({ children, className, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn("hidden sm:block", className)}
        {...props}
      >
        {children}
      </div>
    )
  }
)
DesktopOnly.displayName = "DesktopOnly"

export {
  ResponsiveContainer,
  ResponsiveGrid,
  ResponsiveStack,
  MobileOnly,
  DesktopOnly,
}
