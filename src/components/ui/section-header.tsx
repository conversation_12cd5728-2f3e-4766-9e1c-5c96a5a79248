import * as React from "react"
import { motion } from "framer-motion"
import { cn } from "@/lib/utils"

interface SectionHeaderProps {
  title: string
  description?: string
  icon?: React.ReactNode
  action?: React.ReactNode
  variant?: "default" | "centered" | "minimal"
  className?: string
}

const SectionHeader = React.forwardRef<HTMLDivElement, SectionHeaderProps>(
  ({ title, description, icon, action, variant = "default", className, ...props }, ref) => {
    const variants = {
      default: "text-left",
      centered: "text-center",
      minimal: "text-left border-none pb-4"
    }

    return (
      <motion.div
        ref={ref}
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className={cn(
          "flex flex-col gap-2 pb-6 mb-6",
          variant !== "minimal" && "border-b border-border/40",
          variants[variant],
          className
        )}
        {...props}
      >
        <div className={cn(
          "flex items-center gap-3",
          variant === "centered" && "justify-center",
          action && variant !== "centered" && "justify-between"
        )}>
          <div className="flex items-center gap-3">
            {icon && (
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ duration: 0.2, delay: 0.1 }}
                className="flex-shrink-0 text-primary"
              >
                {icon}
              </motion.div>
            )}
            <div>
              <h2 className="text-2xl font-bold tracking-tight bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text">
                {title}
              </h2>
              <div className="h-1 w-12 bg-gradient-to-r from-primary to-primary/30 rounded-full mt-1" />
            </div>
          </div>
          
          {action && variant !== "centered" && (
            <motion.div
              initial={{ opacity: 0, x: 10 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3, delay: 0.2 }}
            >
              {action}
            </motion.div>
          )}
        </div>
        
        {description && (
          <motion.p
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.3, delay: 0.2 }}
            className={cn(
              "text-muted-foreground leading-relaxed max-w-2xl",
              variant === "centered" && "mx-auto"
            )}
          >
            {description}
          </motion.p>
        )}
        
        {action && variant === "centered" && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.3 }}
            className="flex justify-center mt-2"
          >
            {action}
          </motion.div>
        )}
      </motion.div>
    )
  }
)
SectionHeader.displayName = "SectionHeader"

export { SectionHeader }
