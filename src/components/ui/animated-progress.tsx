import * as React from "react"
import { motion } from "framer-motion"
import { cn } from "@/lib/utils"

interface AnimatedProgressProps {
  value: number
  max?: number
  className?: string
  showValue?: boolean
  variant?: "default" | "success" | "warning" | "danger"
  size?: "sm" | "md" | "lg"
  animated?: boolean
  gradient?: boolean
}

const AnimatedProgress = React.forwardRef<HTMLDivElement, AnimatedProgressProps>(
  ({ 
    value, 
    max = 100, 
    className, 
    showValue = false, 
    variant = "default", 
    size = "md", 
    animated = true,
    gradient = false,
    ...props 
  }, ref) => {
    const percentage = Math.min(Math.max((value / max) * 100, 0), 100)
    
    const variants = {
      default: "bg-primary",
      success: "bg-green-500",
      warning: "bg-yellow-500",
      danger: "bg-red-500"
    }

    const gradientVariants = {
      default: "bg-gradient-to-r from-primary to-primary/80",
      success: "bg-gradient-to-r from-green-500 to-green-400",
      warning: "bg-gradient-to-r from-yellow-500 to-yellow-400",
      danger: "bg-gradient-to-r from-red-500 to-red-400"
    }

    const sizes = {
      sm: "h-1",
      md: "h-2",
      lg: "h-3"
    }

    return (
      <div className={cn("relative", className)} ref={ref} {...props}>
        <div className={cn(
          "w-full bg-muted rounded-full overflow-hidden",
          sizes[size]
        )}>
          <motion.div
            className={cn(
              "h-full rounded-full transition-colors duration-200",
              gradient ? gradientVariants[variant] : variants[variant],
              animated && "relative overflow-hidden"
            )}
            initial={{ width: 0 }}
            animate={{ width: `${percentage}%` }}
            transition={{ 
              duration: 0.8, 
              ease: "easeOut",
              delay: 0.1
            }}
          >
            {animated && (
              <motion.div
                className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent"
                initial={{ x: "-100%" }}
                animate={{ x: "100%" }}
                transition={{
                  duration: 1.5,
                  repeat: Infinity,
                  ease: "easeInOut",
                  delay: 0.5
                }}
              />
            )}
          </motion.div>
        </div>
        
        {showValue && (
          <motion.div
            initial={{ opacity: 0, y: 5 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.2 }}
            className="flex justify-between items-center mt-1 text-xs text-muted-foreground"
          >
            <span>{Math.round(percentage)}%</span>
            <span>{value} / {max}</span>
          </motion.div>
        )}
      </div>
    )
  }
)
AnimatedProgress.displayName = "AnimatedProgress"

interface CircularProgressProps {
  value: number
  max?: number
  size?: number
  strokeWidth?: number
  className?: string
  showValue?: boolean
  variant?: "default" | "success" | "warning" | "danger"
}

const CircularProgress = React.forwardRef<SVGSVGElement, CircularProgressProps>(
  ({ 
    value, 
    max = 100, 
    size = 120, 
    strokeWidth = 8, 
    className, 
    showValue = true,
    variant = "default",
    ...props 
  }, ref) => {
    const percentage = Math.min(Math.max((value / max) * 100, 0), 100)
    const radius = (size - strokeWidth) / 2
    const circumference = radius * 2 * Math.PI
    const strokeDasharray = circumference
    const strokeDashoffset = circumference - (percentage / 100) * circumference

    const colors = {
      default: "stroke-primary",
      success: "stroke-green-500",
      warning: "stroke-yellow-500",
      danger: "stroke-red-500"
    }

    return (
      <div className={cn("relative inline-flex items-center justify-center", className)}>
        <svg
          ref={ref}
          width={size}
          height={size}
          className="transform -rotate-90"
          {...props}
        >
          {/* Background circle */}
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke="currentColor"
            strokeWidth={strokeWidth}
            fill="none"
            className="text-muted/20"
          />
          
          {/* Progress circle */}
          <motion.circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke="currentColor"
            strokeWidth={strokeWidth}
            fill="none"
            strokeLinecap="round"
            className={colors[variant]}
            initial={{ strokeDasharray, strokeDashoffset: circumference }}
            animate={{ strokeDasharray, strokeDashoffset }}
            transition={{ duration: 1, ease: "easeOut", delay: 0.1 }}
          />
        </svg>
        
        {showValue && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3, delay: 0.5 }}
            className="absolute inset-0 flex items-center justify-center"
          >
            <span className="text-lg font-semibold">
              {Math.round(percentage)}%
            </span>
          </motion.div>
        )}
      </div>
    )
  }
)
CircularProgress.displayName = "CircularProgress"

export { AnimatedProgress, CircularProgress }
