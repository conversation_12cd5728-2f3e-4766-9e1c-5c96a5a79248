import * as React from "react"
import { motion, AnimatePresence } from "framer-motion"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { Plus, X } from "lucide-react"

interface FloatingActionButtonProps {
  children?: React.ReactNode
  className?: string
  onClick?: () => void
  icon?: React.ReactNode
  expanded?: boolean
  actions?: Array<{
    icon: React.ReactNode
    label: string
    onClick: () => void
    className?: string
  }>
}

const FloatingActionButton = React.forwardRef<HTMLButtonElement, FloatingActionButtonProps>(
  ({ children, className, onClick, icon = <Plus className="h-5 w-5" />, expanded = false, actions = [], ...props }, ref) => {
    const [isExpanded, setIsExpanded] = React.useState(expanded)

    const handleClick = () => {
      if (actions.length > 0) {
        setIsExpanded(!isExpanded)
      }
      onClick?.()
    }

    return (
      <div className="fixed bottom-6 right-6 z-50">
        <AnimatePresence>
          {isExpanded && actions.length > 0 && (
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              transition={{ duration: 0.2 }}
              className="flex flex-col gap-3 mb-3"
            >
              {actions.map((action, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: 20, scale: 0.8 }}
                  animate={{ opacity: 1, x: 0, scale: 1 }}
                  exit={{ opacity: 0, x: 20, scale: 0.8 }}
                  transition={{ 
                    duration: 0.2, 
                    delay: index * 0.05,
                    ease: "easeOut"
                  }}
                  className="flex items-center gap-3"
                >
                  <motion.span
                    className="bg-background/90 backdrop-blur-sm text-foreground px-3 py-1 rounded-lg text-sm font-medium shadow-lg border border-border/40"
                    whileHover={{ scale: 1.05 }}
                  >
                    {action.label}
                  </motion.span>
                  <Button
                    size="icon"
                    className={cn(
                      "h-12 w-12 rounded-full shadow-lg hover:shadow-xl transition-all duration-200",
                      "bg-primary/90 hover:bg-primary text-primary-foreground",
                      action.className
                    )}
                    onClick={action.onClick}
                    asChild
                  >
                    <motion.button
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.95 }}
                      transition={{ duration: 0.1 }}
                    >
                      {action.icon}
                    </motion.button>
                  </Button>
                </motion.div>
              ))}
            </motion.div>
          )}
        </AnimatePresence>

        <Button
          ref={ref}
          size="icon"
          className={cn(
            "h-14 w-14 rounded-full shadow-lg hover:shadow-xl transition-all duration-200",
            "bg-primary hover:bg-primary/90 text-primary-foreground",
            className
          )}
          onClick={handleClick}
          asChild
          {...props}
        >
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.95 }}
            transition={{ duration: 0.1 }}
          >
            <motion.div
              animate={{ rotate: isExpanded ? 45 : 0 }}
              transition={{ duration: 0.2 }}
            >
              {isExpanded && actions.length > 0 ? <X className="h-6 w-6" /> : icon}
            </motion.div>
            {children}
          </motion.button>
        </Button>
      </div>
    )
  }
)
FloatingActionButton.displayName = "FloatingActionButton"

export { FloatingActionButton }
