import { GroupDetails } from "@/components/chat/GroupDetails"
import { useSupabaseAuth } from "@/contexts/SupabaseAuthContext"
import { useEffect, useState, useRef } from "react"
import { useNavigate } from "react-router-dom"
import { useChatStore, ChatGroup } from "@/stores/chatStore"
import { useBackgroundTheme } from "@/contexts/BackgroundThemeContext"
import { useDocumentTitle } from "@/hooks/useDocumentTitle"
import { Users, UserPlus, MessageSquare, ArrowLeft, Globe, Lock, PlusCircle, Clock, CalendarClock, Search, Filter, TagIcon, X, Settings2, Sparkles, Crown, Star, TrendingUp, Zap, Heart, Award, Target } from "lucide-react"
import { Button } from "@/components/ui/button"

import { SmallDeviceWarning } from '@/components/ui/SmallDeviceWarning'
import { FeedbackWidget } from '@/components/FeedbackWidget'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { useToast } from "@/components/ui/use-toast"
// Removed Firebase imports - now using Supabase
import { CreateGroup } from "@/components/chat/CreateGroup"
import { JoinGroup } from "@/components/chat/JoinGroup"
import Header from "@/components/shared/Header"
import { getGroups, getPublicGroups, getGroupStats, joinGroup, GroupStats as SupabaseGroupStats } from "@/utils/supabase"

// Extended group interface with statistics
interface GroupWithStats extends ChatGroup {
  stats: SupabaseGroupStats;
}
import { TasksDropdown } from "@/components/productivity/TasksDropdown"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"

// Add animation keyframes and utility classes for the animated gradient orbs
const animationStyles = `
  @keyframes blob {
    0% {
      transform: translate(0px, 0px) scale(1);
    }
    33% {
      transform: translate(30px, -50px) scale(1.1);
    }
    66% {
      transform: translate(-20px, 20px) scale(0.9);
    }
    100% {
      transform: translate(0px, 0px) scale(1);
    }
  }
  .animate-blob {
    animation: blob 7s infinite;
  }
  .animation-delay-2000 {
    animation-delay: 2s;
  }
  .animation-delay-4000 {
    animation-delay: 4s;
  }
  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
  }
  .animate-fadeIn {
    animation: fadeIn 0.8s ease-out forwards;
  }
`;



// Note: GroupWithStats is already defined above with SupabaseGroupStats

// Define a type for public groups with activity level
interface PublicGroup {
  id: string;
  name: string;
  description: string;
  memberCount: number;
  ownerId: string;
  createdAt: Date;
  tags: string[];
  isUserMember: boolean;
  totalStudyTime: number;
  activityLevel: 'high' | 'medium' | 'low';
}

export default function Groups() {
  useDocumentTitle("Study Groups - IsotopeAI");
  const { user, loading: authLoading } = useSupabaseAuth()
  const navigate = useNavigate()
  const { groups, setGroups } = useChatStore()
  const { getBackgroundStyle } = useBackgroundTheme()
  const [activeTab, setActiveTab] = useState("myGroups")
  const contentRef = useRef<HTMLDivElement>(null)
  const [isCreateOpen, setIsCreateOpen] = useState(false)
  const [isJoinOpen, setIsJoinOpen] = useState(false)
  const [publicGroups, setPublicGroups] = useState<PublicGroup[]>([])
  const [filteredPublicGroups, setFilteredPublicGroups] = useState<PublicGroup[]>([])
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedTags, setSelectedTags] = useState<string[]>([])
  const [availableTags, setAvailableTags] = useState<string[]>([])
  const [joiningGroupId, setJoiningGroupId] = useState<string | null>(null)
  const { toast } = useToast()
  const [selectedGroupId, setSelectedGroupId] = useState<string | null>(null)
  const [isLoadingGroups, setIsLoadingGroups] = useState(true)
  const [isLoadingPublicGroups, setIsLoadingPublicGroups] = useState(false)
  // Task state is now handled in the TasksDropdown component

  useEffect(() => {
    if (!authLoading && !user) {
      navigate('/login')
    }
  }, [user, authLoading, navigate])

  // Fetch user's groups from Supabase with statistics
  useEffect(() => {
    if (!user) return

    const loadGroupsWithStats = async () => {
      setIsLoadingGroups(true)
      try {
        console.log("Fetching user's groups...")
        const userGroups = await getGroups(user.id)

        // Load statistics for each group
        const groupsWithStats = await Promise.all(
          userGroups.map(async (group) => {
            const stats = await getGroupStats(group.id)
            // Convert totalStudyTime from seconds to minutes for display
            const convertedStats = {
              ...stats,
              totalStudyTime: stats.totalStudyTime / 60
            };
            return {
              id: group.id,
              name: group.name,
              description: group.description || '',
              members: group.members || [],
              createdAt: group.createdAt,
              createdBy: group.createdBy,
              owner_id: group.owner_id,
              inviteCode: group.inviteCode || '',
              isPublic: group.isPublic || false,
              lastMessage: group.last_message ? {
                id: (group.last_message as any)?.id || '',
                content: (group.last_message as any)?.content || '',
                senderId: (group.last_message as any)?.senderId || '',
                timestamp: (group.last_message as any)?.timestamp || Date.now(),
                groupId: group.id
              } : undefined,
              stats: convertedStats
            } as GroupWithStats
          })
        )

        setGroups(groupsWithStats)
      } catch (error) {
        console.error('Error loading groups:', error)
        toast({
          variant: "destructive",
          title: "Error",
          description: "Failed to load groups"
        })
      } finally {
        setIsLoadingGroups(false)
      }
    }

    loadGroupsWithStats()
  }, [user, setGroups, toast]);

  // Fetch public groups when the Discover tab is active
  useEffect(() => {
    if (activeTab === "discover" && user) {
      fetchPublicGroups()
    }
  }, [activeTab, user])

  // Debug log to check groups
  useEffect(() => {
    console.log("Current groups in state:", groups)
  }, [groups])

  // TODO: Implement Supabase group stats fetching

  // Helper function to format time duration (input is in minutes)
  const formatStudyTime = (minutes: number): string => {
    if (!minutes) return '0h';

    const hours = Math.floor(minutes / 60);
    const remainingMinutes = Math.floor(minutes % 60);

    if (hours > 0) {
      return `${hours}h${remainingMinutes > 0 ? ` ${remainingMinutes}m` : ''}`;
    }
    return `${remainingMinutes}m`;
  };

  // Helper function to format relative time
  const getRelativeTimeString = (date: Date | null): string => {
    if (!date) return 'Never';
    
    const now = new Date();
    const diffInMs = now.getTime() - date.getTime();
    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));
    
    if (diffInDays === 0) return 'Today';
    if (diffInDays === 1) return 'Yesterday';
    if (diffInDays < 7) return `${diffInDays} days ago`;
    if (diffInDays < 30) return `${Math.floor(diffInDays / 7)} weeks ago`;
    return `${Math.floor(diffInDays / 30)} months ago`;
  };

  const fetchPublicGroups = async () => {
    if (!user) return

    setIsLoadingPublicGroups(true)
    try {
      const publicGroupsData = await getPublicGroups(user.id)

      // Transform to PublicGroup format with stats
      const publicGroupsWithStats = await Promise.all(
        publicGroupsData.map(async (group) => {
          const stats = await getGroupStats(group.id)

          // Determine activity level based on last active date
          const activityLevel: 'high' | 'medium' | 'low' = (() => {
            if (!stats.lastActiveDate) return 'low'
            const daysSinceActive = Math.floor((Date.now() - stats.lastActiveDate.getTime()) / (1000 * 60 * 60 * 24))
            if (daysSinceActive <= 1) return 'high'
            if (daysSinceActive <= 7) return 'medium'
            return 'low'
          })()

          return {
            id: group.id,
            name: group.name,
            description: group.description || '',
            memberCount: (group.members || []).length,
            ownerId: group.owner_id || group.createdBy,
            createdAt: new Date(group.createdAt || Date.now()),
            tags: [], // TODO: Add tags support to groups table
            isUserMember: false, // Already filtered out in getPublicGroups
            totalStudyTime: stats.totalStudyTime / 60, // Convert to minutes for display
            activityLevel
          } as PublicGroup
        })
      )

      setPublicGroups(publicGroupsWithStats)
      setFilteredPublicGroups(publicGroupsWithStats)

      // Extract unique tags (when tags are implemented)
      const allTags = publicGroupsWithStats.flatMap(group => group.tags)
      setAvailableTags([...new Set(allTags)])
    } catch (error) {
      console.error('Error fetching public groups:', error)
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to load public groups"
      })
    } finally {
      setIsLoadingPublicGroups(false)
    }
  }

  // Helper function to format study time for group display (input is in minutes)
  const formatGroupStudyTime = (minutes: number): string => {
    if (!minutes) return '0h';

    const hours = Math.floor(minutes / 60);

    if (hours > 0) {
      return `${hours} ${hours === 1 ? 'hour' : 'hours'}`;
    }
    return 'Less than 1 hour';
  };

  // Apply filters when search query or tags change
  useEffect(() => {
    if (!publicGroups.length) return;
    
    const filtered = publicGroups.filter((group: PublicGroup) => {
      // Apply search query filter
      const matchesSearch = searchQuery === "" || 
        group.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        group.description.toLowerCase().includes(searchQuery.toLowerCase());
      
      // Apply tags filter
      const matchesTags = selectedTags.length === 0 || 
        selectedTags.some(tag => group.tags.includes(tag));
      
      return matchesSearch && matchesTags;
    });
    
    setFilteredPublicGroups(filtered);
  }, [searchQuery, selectedTags, publicGroups]);

  // Toggle tag selection
  const toggleTag = (tag: string) => {
    if (selectedTags.includes(tag)) {
      setSelectedTags(selectedTags.filter(t => t !== tag));
    } else {
      setSelectedTags([...selectedTags, tag]);
    }
  };

  // Clear all filters
  const clearFilters = () => {
    setSearchQuery("");
    setSelectedTags([]);
  };

  const handleJoinPublicGroup = async (groupId: string) => {
    if (!user) return

    setJoiningGroupId(groupId)
    try {
      // Join the group using Supabase
      await joinGroup(groupId, user.id)

      // Remove from public groups list
      setPublicGroups(publicGroups.filter(g => g.id !== groupId))
      setFilteredPublicGroups(filteredPublicGroups.filter(g => g.id !== groupId))

      toast({
        title: "Success",
        description: "You have joined the group"
      })

      // Switch to My Groups tab and refresh groups
      setActiveTab("myGroups")

      // Refresh user's groups to include the newly joined group
      const userGroups = await getGroups(user.id)
      const groupsWithStats = await Promise.all(
        userGroups.map(async (group) => {
          const stats = await getGroupStats(group.id)
          return {
            id: group.id,
            name: group.name,
            description: group.description || '',
            members: group.members || [],
            createdAt: group.createdAt,
            createdBy: group.createdBy,
            owner_id: group.owner_id,
            inviteCode: group.inviteCode || '',
            isPublic: group.isPublic || false,
            lastMessage: group.last_message ? {
              id: (group.last_message as any)?.id || '',
              content: (group.last_message as any)?.content || '',
              senderId: (group.last_message as any)?.senderId || '',
              timestamp: (group.last_message as any)?.timestamp || Date.now(),
              groupId: group.id
            } : undefined,
            stats
          } as GroupWithStats
        })
      )
      setGroups(groupsWithStats)

    } catch (error) {
      console.error('Error joining public group:', error)
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to join group. Please try again."
      })
    } finally {
      setJoiningGroupId(null)
    }
  }



  if (authLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary" />
      </div>
    )
  }

  return (
    // Apply theme-aware background and text color
    <div className="relative min-h-screen w-full bg-background dark:bg-gradient-to-b dark:from-[#0f172a] dark:to-[#1e293b] text-foreground overflow-hidden">
      <SmallDeviceWarning />

      {/* Inject animation styles */}
      <style dangerouslySetInnerHTML={{ __html: animationStyles }} />

      {/* Background pattern overlay (Dark mode only) */}
      <div className="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiMyMDIwMjAiIGZpbGwtb3BhY2l0eT0iMC4wNSI+PHBhdGggZD0iTTM2IDM0djZoNnYtNmgtNnptNiA2djZoNnYtNmgtNnptLTEyIDBoNnY2aC02di02em0xMiAwaDZ2NnYtNnoiLz48cGF0aCBkPSJNMTIgMzZ2Nmg2di02aC02em0wIDZoNnY2aC02di02em0wLTEyaDZ2NnYtNnoiLz48L2c+PC9nPjwvc3ZnPg==')] dark:block hidden"></div>

      {/* Gradient overlay (Dark mode only) */}
      <div className="absolute inset-0 bg-gradient-to-tr from-indigo-900/30 via-transparent to-pink-900/20 dark:block hidden"></div>

      {/* Enhanced Background elements */}
      <div className={`absolute inset-0 ${getBackgroundStyle()} opacity-40 dark:block hidden`} />

      {/* Majestic animated gradient orbs */}
      <div className="absolute top-1/4 left-1/4 w-[500px] h-[500px] bg-gradient-to-br from-indigo-600/30 via-purple-600/20 to-pink-600/30 rounded-full filter blur-[120px] animate-blob dark:block hidden"></div>
      <div className="absolute top-3/4 right-1/4 w-[400px] h-[400px] bg-gradient-to-br from-emerald-600/25 via-cyan-600/20 to-blue-600/25 rounded-full filter blur-[100px] animate-blob animation-delay-2000 dark:block hidden"></div>
      <div className="absolute bottom-1/4 left-1/2 w-[600px] h-[600px] bg-gradient-to-br from-violet-600/20 via-fuchsia-600/15 to-rose-600/20 rounded-full filter blur-[140px] animate-blob animation-delay-4000 dark:block hidden"></div>

      {/* Additional floating elements for depth */}
      <div className="absolute top-1/2 right-1/3 w-32 h-32 bg-gradient-to-br from-amber-500/10 to-orange-500/10 rounded-full filter blur-[60px] animate-pulse-slow dark:block hidden"></div>
      <div className="absolute bottom-1/3 left-1/4 w-48 h-48 bg-gradient-to-br from-teal-500/15 to-cyan-500/15 rounded-full filter blur-[80px] animate-float-slow dark:block hidden"></div>

      {/* Import shared Header component */}
      <Header />

      {/* Draggable Tasks Dropdown */}
      <TasksDropdown
        groupId={selectedGroupId || undefined}
      />

      {/* Main content */}
      <div className="relative z-10 min-h-screen">
        <div className="max-w-[1400px] mx-auto px-4 sm:px-8 pt-32 pb-24" ref={contentRef}>
          {/* Tasks are now shown in a hover menu via TasksDropdown */}
          {selectedGroupId ? (
            <div className="flex items-center mb-12">
              <Button
                variant="ghost"
                size="icon"
                className="h-12 w-12 rounded-xl bg-gradient-to-br from-white/10 to-white/5 dark:from-white/10 dark:to-white/5 hover:from-white/20 hover:to-white/10 border border-white/20 dark:border-white/20 text-foreground backdrop-blur-sm mr-6 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
                onClick={() => setSelectedGroupId(null)}
              >
                <ArrowLeft className="h-5 w-5" />
              </Button>
              <div className="flex flex-col">
                <h1 className="text-3xl sm:text-4xl font-bold bg-gradient-to-r from-indigo-400 via-purple-400 to-pink-400 bg-clip-text text-transparent">
                  Group Details
                </h1>
                <div className="h-1 w-24 bg-gradient-to-r from-indigo-500 to-pink-500 rounded-full mt-2"></div>
              </div>
            </div>
          ) : (
            <div className="text-center mb-16">
              <div className="inline-flex items-center gap-3 mb-6">
                <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-indigo-500/20 to-purple-500/20 flex items-center justify-center backdrop-blur-sm border border-white/10">
                  <Users className="h-6 w-6 text-indigo-400" />
                </div>
                <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-purple-500/20 to-pink-500/20 flex items-center justify-center backdrop-blur-sm border border-white/10">
                  <Sparkles className="h-6 w-6 text-purple-400" />
                </div>
                <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-pink-500/20 to-rose-500/20 flex items-center justify-center backdrop-blur-sm border border-white/10">
                  <Crown className="h-6 w-6 text-pink-400" />
                </div>
              </div>
              <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold bg-gradient-to-r from-indigo-400 via-purple-400 to-pink-400 bg-clip-text text-transparent mb-4">
                Study Groups
              </h1>
              <p className="text-lg text-muted-foreground dark:text-white/60 max-w-2xl mx-auto leading-relaxed">
                Join collaborative study groups, share knowledge, and achieve academic excellence together
              </p>
              <div className="h-1 w-32 bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 rounded-full mx-auto mt-6"></div>
            </div>
          )}

          <Tabs
            value={activeTab}
            defaultValue="myGroups"
            className="w-full"
            onValueChange={(value) => {
              console.log('Tab value changed to:', value);
              setActiveTab(value);
            }}
          >
            <div className="flex justify-center mb-12 overflow-x-auto pb-2 relative">
              {/* Enhanced TabsList with glassmorphism */}
              <TabsList className="bg-white/10 dark:bg-gradient-to-r dark:from-slate-800/40 dark:to-slate-900/40 p-2 gap-2 inline-flex flex-wrap justify-center w-full max-w-[700px] mx-auto sm:flex-nowrap rounded-2xl border border-white/20 dark:border-white/10 shadow-2xl backdrop-blur-xl">
                {/* Enhanced TabsTrigger with better gradients */}
                <TabsTrigger
                  value="myGroups"
                  className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-indigo-500 data-[state=active]:to-purple-600 data-[state=active]:text-white data-[state=active]:shadow-lg data-[state=active]:shadow-indigo-500/25 text-muted-foreground dark:text-white/80 text-sm px-6 py-3 flex-1 rounded-xl transition-all duration-300 hover:bg-white/10 dark:hover:bg-white/5 font-medium"
                >
                  <Users className="h-4 w-4 mr-2" />
                  My Groups
                </TabsTrigger>
                <TabsTrigger
                  value="discover"
                  className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-pink-500 data-[state=active]:to-rose-600 data-[state=active]:text-white data-[state=active]:shadow-lg data-[state=active]:shadow-pink-500/25 text-muted-foreground dark:text-white/80 text-sm px-6 py-3 flex-1 rounded-xl transition-all duration-300 hover:bg-white/10 dark:hover:bg-white/5 font-medium"
                >
                  <Globe className="h-4 w-4 mr-2" />
                  Discover
                </TabsTrigger>
                <TabsTrigger
                  value="joinCreate"
                  className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-emerald-500 data-[state=active]:to-teal-600 data-[state=active]:text-white data-[state=active]:shadow-lg data-[state=active]:shadow-emerald-500/25 text-muted-foreground dark:text-white/80 text-sm px-6 py-3 flex-1 rounded-xl transition-all duration-300 hover:bg-white/10 dark:hover:bg-white/5 font-medium"
                >
                  <PlusCircle className="h-4 w-4 mr-2" />
                  Join/Create
                </TabsTrigger>
              </TabsList>
            </div>

            <TabsContent value="myGroups" data-tab="myGroups">
              {selectedGroupId ? (
                /* Enhanced group details card */
                <Card className="bg-white/5 dark:bg-gradient-to-br dark:from-slate-800/40 dark:to-slate-900/40 border border-white/20 dark:border-white/10 backdrop-blur-2xl rounded-3xl shadow-2xl overflow-hidden">
                  <CardHeader className="border-b border-white/10 dark:border-white/10 pb-6 bg-gradient-to-r from-indigo-500/10 to-purple-500/10">
                    <CardTitle className="text-xl font-semibold flex items-center gap-3 text-card-foreground">
                      <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-indigo-500/20 to-purple-500/20 flex items-center justify-center">
                        <Users className="h-5 w-5 text-indigo-400" />
                      </div>
                      <span className="bg-gradient-to-r from-indigo-400 to-purple-400 bg-clip-text text-transparent">Group Details</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="pt-8">
                    <GroupDetails groupId={selectedGroupId} />
                  </CardContent>
                </Card>
              ) : (
                /* Enhanced grid of group cards */
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
                  {/* Enhanced loading state */}
                  {isLoadingGroups ? (
                    // Beautiful skeleton loaders
                    <>
                      {[1, 2, 3, 4, 5, 6].map((i) => (
                        <div
                          key={i}
                          className="flex flex-col p-8 bg-white/5 dark:bg-slate-800/30 rounded-2xl border border-white/10 dark:border-white/5 min-h-[280px] animate-pulse backdrop-blur-sm"
                          style={{ animationDelay: `${i * 100}ms` }}
                        >
                          <div className="flex items-center justify-between mb-4">
                            <div className="w-32 h-6 bg-gradient-to-r from-indigo-500/20 to-purple-500/20 rounded-lg"></div>
                            <div className="w-20 h-5 bg-gradient-to-r from-pink-500/20 to-rose-500/20 rounded-full"></div>
                          </div>
                          <div className="w-full h-16 bg-gradient-to-r from-slate-500/10 to-slate-600/10 rounded-lg mb-4"></div>
                          <div className="grid grid-cols-2 gap-3 mb-4">
                            <div className="h-16 bg-gradient-to-br from-indigo-500/10 to-purple-500/10 rounded-lg"></div>
                            <div className="h-16 bg-gradient-to-br from-pink-500/10 to-rose-500/10 rounded-lg"></div>
                          </div>
                          <div className="mt-auto pt-4 border-t border-white/10 dark:border-white/5">
                            <div className="w-28 h-4 bg-gradient-to-r from-emerald-500/20 to-teal-500/20 rounded"></div>
                          </div>
                        </div>
                      ))}
                    </>
                  ) : groups && groups.length > 0 ? (
                    <>
                      {/* Enhanced Group cards */}
                      {groups.map((group, index) => (
                        <div
                          key={group.id}
                          onClick={() => setSelectedGroupId(group.id)}
                          className={`group relative flex flex-col p-8 bg-white/5 dark:bg-gradient-to-br dark:from-slate-800/40 dark:to-slate-900/40 hover:from-indigo-900/30 hover:to-purple-900/30 rounded-2xl border border-white/20 dark:border-white/10 hover:border-white/30 transition-all duration-500 shadow-xl hover:shadow-2xl hover:shadow-indigo-500/10 hover:scale-[1.03] cursor-pointer animate-fadeIn overflow-hidden backdrop-blur-sm`}
                          style={{ animationDelay: `${index * 100}ms` }}
                        >
                          {/* Enhanced decorative elements */}
                          <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500"></div>
                          <div className="absolute -top-20 -right-20 w-40 h-40 bg-gradient-to-br from-indigo-500/10 to-purple-500/10 rounded-full blur-3xl group-hover:from-indigo-500/20 group-hover:to-purple-500/20 transition-all duration-500"></div>

                          <div className="relative z-10">
                            <div className="flex items-center justify-between mb-4">
                              <h3 className="text-xl font-semibold text-card-foreground dark:group-hover:text-white transition-colors bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text">{group.name}</h3>
                              <div className={`text-xs px-3 py-1.5 rounded-full flex items-center gap-2 font-medium backdrop-blur-sm ${
                                group.isPublic
                                  ? 'bg-gradient-to-r from-indigo-500/20 to-purple-500/20 text-indigo-300 border border-indigo-500/30'
                                  : 'bg-gradient-to-r from-slate-500/20 to-slate-600/20 text-slate-300 border border-slate-500/30'
                              }`}>
                                {group.isPublic ? (
                                  <>
                                    <Globe className="h-3.5 w-3.5" />
                                    <span>Public</span>
                                  </>
                                ) : (
                                  <>
                                    <Lock className="h-3.5 w-3.5" />
                                    <span>Private</span>
                                  </>
                                )}
                              </div>
                            </div>

                            <p className="text-sm text-muted-foreground dark:text-white/70 mb-6 line-clamp-2 leading-relaxed">{group.description || 'No description available'}</p>

                            {/* Enhanced Stats section */}
                            <div className="grid grid-cols-2 gap-4 mb-6">
                              <div className="bg-gradient-to-br from-indigo-500/10 to-purple-500/10 border border-indigo-500/20 p-4 rounded-xl flex flex-col items-center backdrop-blur-sm group-hover:from-indigo-500/20 group-hover:to-purple-500/20 transition-all duration-300">
                                <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-indigo-500/20 to-purple-500/20 flex items-center justify-center mb-2">
                                  <Clock className="h-4 w-4 text-indigo-400" />
                                </div>
                                <span className="text-lg font-bold text-foreground">{formatStudyTime((group as GroupWithStats).stats?.totalStudyTime || 0)}</span>
                                <span className="text-xs text-muted-foreground font-medium">Study Time</span>
                              </div>

                              <div className="bg-gradient-to-br from-pink-500/10 to-rose-500/10 border border-pink-500/20 p-4 rounded-xl flex flex-col items-center backdrop-blur-sm group-hover:from-pink-500/20 group-hover:to-rose-500/20 transition-all duration-300">
                                <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-pink-500/20 to-rose-500/20 flex items-center justify-center mb-2">
                                  <Users className="h-4 w-4 text-pink-400" />
                                </div>
                                <span className="text-lg font-bold text-foreground">{group.members.length}</span>
                                <span className="text-xs text-muted-foreground font-medium">Members</span>
                              </div>
                            </div>

                            <div className="flex items-center justify-between text-sm text-muted-foreground dark:text-white/60 mt-auto pt-4 border-t border-white/10 dark:border-white/10">
                              <div className="flex items-center gap-2">
                                <div className="w-6 h-6 rounded-lg bg-gradient-to-br from-emerald-500/20 to-teal-500/20 flex items-center justify-center">
                                  <CalendarClock className="h-3 w-3 text-emerald-400" />
                                </div>
                                <span className="font-medium">Active {getRelativeTimeString((group as GroupWithStats).stats?.lastActiveDate)}</span>
                              </div>

                              <div className="flex items-center gap-2">
                                <div className="w-6 h-6 rounded-lg bg-gradient-to-br from-cyan-500/20 to-blue-500/20 flex items-center justify-center">
                                  <MessageSquare className="h-3 w-3 text-cyan-400" />
                                </div>
                                <span className="font-medium">{(group as GroupWithStats).stats?.totalSessions || 0} sessions</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}

                      {/* Enhanced Create New Group Card */}
                      <div
                        onClick={() => setIsCreateOpen(true)}
                        className="group relative flex flex-col items-center justify-center p-8 bg-gradient-to-br from-indigo-500/5 to-purple-500/5 hover:from-indigo-500/10 hover:to-purple-500/10 rounded-2xl border-2 border-dashed border-indigo-500/30 hover:border-indigo-500/50 transition-all duration-500 shadow-xl hover:shadow-2xl hover:shadow-indigo-500/10 hover:scale-[1.03] cursor-pointer min-h-[280px] animate-fadeIn backdrop-blur-sm overflow-hidden"
                        style={{ animationDelay: `${groups.length * 100}ms` }}
                      >
                        <div className="absolute -top-10 -right-10 w-32 h-32 bg-gradient-to-br from-indigo-500/10 to-purple-500/10 rounded-full blur-2xl group-hover:from-indigo-500/20 group-hover:to-purple-500/20 transition-all duration-500"></div>

                        <div className="relative z-10 flex flex-col items-center">
                          <div className="w-20 h-20 rounded-2xl bg-gradient-to-br from-indigo-500/20 to-purple-500/20 group-hover:from-indigo-500/30 group-hover:to-purple-500/30 flex items-center justify-center mb-6 transition-all duration-300 group-hover:scale-110">
                            <PlusCircle className="h-10 w-10 text-indigo-400 group-hover:text-indigo-300 transition-colors" />
                          </div>
                          <h3 className="text-xl font-semibold text-muted-foreground dark:text-white/70 group-hover:text-foreground dark:group-hover:text-white transition-colors mb-2">Create New Group</h3>
                          <p className="text-sm text-muted-foreground/80 dark:text-white/50 text-center max-w-[200px] leading-relaxed">Start your own study community</p>
                        </div>
                      </div>

                      {/* Enhanced Join Group Card */}
                      <div
                        onClick={() => setIsJoinOpen(true)}
                        className="group relative flex flex-col items-center justify-center p-8 bg-gradient-to-br from-pink-500/5 to-rose-500/5 hover:from-pink-500/10 hover:to-rose-500/10 rounded-2xl border-2 border-dashed border-pink-500/30 hover:border-pink-500/50 transition-all duration-500 shadow-xl hover:shadow-2xl hover:shadow-pink-500/10 hover:scale-[1.03] cursor-pointer min-h-[280px] animate-fadeIn backdrop-blur-sm overflow-hidden"
                        style={{ animationDelay: `${(groups.length + 1) * 100}ms` }}
                      >
                        <div className="absolute -top-10 -left-10 w-32 h-32 bg-gradient-to-br from-pink-500/10 to-rose-500/10 rounded-full blur-2xl group-hover:from-pink-500/20 group-hover:to-rose-500/20 transition-all duration-500"></div>

                        <div className="relative z-10 flex flex-col items-center">
                          <div className="w-20 h-20 rounded-2xl bg-gradient-to-br from-pink-500/20 to-rose-500/20 group-hover:from-pink-500/30 group-hover:to-rose-500/30 flex items-center justify-center mb-6 transition-all duration-300 group-hover:scale-110">
                            <UserPlus className="h-10 w-10 text-pink-400 group-hover:text-pink-300 transition-colors" />
                          </div>
                          <h3 className="text-xl font-semibold text-muted-foreground dark:text-white/70 group-hover:text-foreground dark:group-hover:text-white transition-colors mb-2">Join a Group</h3>
                          <p className="text-sm text-muted-foreground/80 dark:text-white/50 text-center max-w-[200px] leading-relaxed">Connect with existing communities</p>
                        </div>
                      </div>
                    </>
                  ) : (
                    // Enhanced no groups state
                    <div className="col-span-full flex flex-col items-center justify-center py-20 text-center animate-fadeIn">
                      <div className="relative mb-8">
                        <div className="w-32 h-32 rounded-3xl bg-gradient-to-br from-indigo-500/20 via-purple-500/20 to-pink-500/20 flex items-center justify-center backdrop-blur-sm border border-white/10">
                          <Users className="w-16 h-16 text-indigo-400" />
                        </div>
                        <div className="absolute -top-4 -right-4 w-8 h-8 rounded-full bg-gradient-to-br from-pink-500/30 to-rose-500/30 flex items-center justify-center">
                          <Sparkles className="w-4 h-4 text-pink-300" />
                        </div>
                      </div>
                      <h3 className="text-3xl font-bold bg-gradient-to-r from-indigo-400 via-purple-400 to-pink-400 bg-clip-text text-transparent mb-4">No Groups Yet</h3>
                      <p className="text-muted-foreground dark:text-white/60 max-w-lg mb-10 text-lg leading-relaxed">
                        You haven't joined any study groups yet. Create your first group or join an existing one to start your collaborative learning journey.
                      </p>
                      <div className="flex flex-col sm:flex-row gap-4">
                        <Button
                          variant="outline"
                          onClick={() => setIsJoinOpen(true)}
                          className="bg-white/5 dark:bg-white/5 hover:bg-white/10 dark:hover:bg-white/10 border-white/20 dark:border-white/20 text-foreground dark:text-white backdrop-blur-sm px-8 py-3 text-lg"
                        >
                          <UserPlus className="h-5 w-5 mr-2" />
                          Join Group
                        </Button>
                        <Button
                          onClick={() => setIsCreateOpen(true)}
                          className="bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 shadow-lg shadow-indigo-500/25 px-8 py-3 text-lg"
                        >
                          <PlusCircle className="h-5 w-5 mr-2" />
                          Create Group
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </TabsContent>

            <TabsContent value="discover" data-tab="discover">
              {/* Enhanced Discover Card */}
              <Card className="bg-white/5 dark:bg-gradient-to-br dark:from-slate-800/40 dark:to-slate-900/40 border border-white/20 dark:border-white/10 backdrop-blur-2xl rounded-3xl shadow-2xl overflow-hidden">
                <CardHeader className="border-b border-white/10 dark:border-white/10 pb-6 bg-gradient-to-r from-pink-500/10 to-purple-500/10">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-xl font-semibold flex items-center gap-3 text-card-foreground">
                      <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-pink-500/20 to-purple-500/20 flex items-center justify-center">
                        <Globe className="h-5 w-5 text-pink-400" />
                      </div>
                      <span className="bg-gradient-to-r from-pink-400 to-purple-400 bg-clip-text text-transparent">Discover Public Groups</span>
                    </CardTitle>
                    {/* Enhanced Refresh Button */}
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-10 w-10 p-0 rounded-xl bg-gradient-to-br from-white/10 to-white/5 hover:from-white/20 hover:to-white/10 border border-white/20 text-foreground backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
                      onClick={fetchPublicGroups}
                      disabled={isLoadingPublicGroups}
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="18"
                        height="18"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className={`${isLoadingPublicGroups ? 'animate-spin' : ''}`}
                      >
                        <path d="M21 12a9 9 0 0 0-9-9 9.75 9.75 0 0 0-6.74 2.74L3 8" />
                        <path d="M3 3v5h5" />
                        <path d="M3 12a9 9 0 0 0 9 9 9.75 9.75 0 0 0 6.74-2.74L21 16" />
                        <path d="M16 21h5v-5" />
                      </svg>
                    </Button>
                  </div>
                </CardHeader>
                <CardContent className="pt-8">
                  {isLoadingPublicGroups ? (
                    <div className="flex justify-center py-12">
                      <div className="relative">
                        <div className="animate-spin rounded-full h-12 w-12 border-4 border-pink-500/20 border-t-pink-500" />
                        <div className="absolute inset-0 rounded-full bg-gradient-to-r from-pink-500/10 to-purple-500/10 blur-xl"></div>
                      </div>
                    </div>
                  ) : publicGroups.length === 0 ? (
                    <div className="flex flex-col items-center justify-center py-16 text-center">
                      <div className="relative mb-8">
                        <div className="w-24 h-24 rounded-2xl bg-gradient-to-br from-pink-500/20 via-purple-500/20 to-indigo-500/20 flex items-center justify-center backdrop-blur-sm border border-white/10">
                          <Globe className="w-12 h-12 text-pink-400" />
                        </div>
                        <div className="absolute -top-2 -right-2 w-6 h-6 rounded-full bg-gradient-to-br from-amber-500/30 to-orange-500/30 flex items-center justify-center">
                          <Search className="w-3 h-3 text-amber-300" />
                        </div>
                      </div>
                      <h3 className="text-2xl font-bold bg-gradient-to-r from-pink-400 to-purple-400 bg-clip-text text-transparent mb-3">No Public Groups Available</h3>
                      <p className="text-muted-foreground dark:text-white/60 max-w-lg mb-8 text-lg leading-relaxed">
                        There are no public groups available to join at the moment. You can create your own group or join a private group with an invite code.
                      </p>
                      <div className="flex flex-col sm:flex-row gap-4">
                        <Button
                          variant="outline"
                          onClick={() => setIsJoinOpen(true)}
                          className="bg-white/5 dark:bg-white/5 hover:bg-white/10 dark:hover:bg-white/10 border-white/20 dark:border-white/20 text-foreground dark:text-white backdrop-blur-sm px-6 py-3"
                        >
                          <Lock className="h-4 w-4 mr-2" />
                          Join Private Group
                        </Button>
                        <Button
                          onClick={() => setActiveTab("joinCreate")}
                          className="bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-600 hover:to-purple-700 shadow-lg shadow-pink-500/25 px-6 py-3"
                        >
                          <PlusCircle className="h-4 w-4 mr-2" />
                          Create Group
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div className="space-y-6">
                      {/* Search and filter section */}
                      <div className="space-y-4">
                        {/* Search bar */}
                        <div className="relative">
                          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                          <Input 
                            placeholder="Search groups by name or description..." 
                            className="pl-10 bg-background/50 dark:bg-white/5 border-border dark:border-white/10"
                            value={searchQuery}
                            onChange={(e) => setSearchQuery(e.target.value)}
                          />
                        </div>
                        
                        {/* Filter section */}
                        <div className="flex flex-col space-y-2">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2 text-sm text-muted-foreground dark:text-white/70">
                              <TagIcon className="h-4 w-4" />
                              <span>Filter by subjects & tags</span>
                            </div>
                            
                            {(searchQuery || selectedTags.length > 0) && (
                              <Button 
                                variant="ghost" 
                                size="sm" 
                                onClick={clearFilters}
                                className="h-7 px-2 text-xs text-muted-foreground hover:text-foreground flex items-center gap-1"
                              >
                                <X className="h-3 w-3" />
                                Clear filters
                              </Button>
                            )}
                          </div>
                          
                          <div className="flex items-center gap-2">
                            <Popover>
                              <PopoverTrigger asChild>
                                <Button 
                                  variant="outline" 
                                  size="sm" 
                                  className="bg-background/50 dark:bg-white/5 border-border dark:border-white/10 text-muted-foreground dark:text-white/70 flex items-center gap-1"
                                >
                                  <Filter className="h-3.5 w-3.5" />
                                  <span>Select Tags</span>
                                  {selectedTags.length > 0 && (
                                    <Badge 
                                      variant="secondary" 
                                      className="ml-1 text-xs bg-indigo-500/20 text-indigo-500 dark:bg-indigo-500/30 dark:text-indigo-300 border-none"
                                    >
                                      {selectedTags.length}
                                    </Badge>
                                  )}
                                </Button>
                              </PopoverTrigger>
                              <PopoverContent 
                                className="w-64 max-h-64 overflow-y-auto bg-card dark:bg-slate-800/90 border-border dark:border-white/10"
                                align="start"
                              >
                                <div className="p-1">
                                  <div className="flex items-center justify-between mb-2">
                                    <span className="text-sm font-medium">Available Tags</span>
                                    {selectedTags.length > 0 && (
                                      <Button 
                                        variant="ghost" 
                                        size="sm" 
                                        onClick={() => setSelectedTags([])}
                                        className="h-6 px-2 text-xs text-muted-foreground hover:text-foreground flex items-center gap-1"
                                      >
                                        <X className="h-3 w-3" />
                                        Clear
                                      </Button>
                                    )}
                                  </div>
                                  <div className="grid grid-cols-2 gap-1">
                                    {availableTags.map(tag => (
                                      <Badge 
                                        key={tag}
                                        variant={selectedTags.includes(tag) ? "default" : "outline"} 
                                        className={`cursor-pointer truncate ${
                                          selectedTags.includes(tag) 
                                            ? "bg-indigo-500 hover:bg-indigo-600" 
                                            : "bg-background/50 dark:bg-white/5 hover:bg-accent"
                                        }`}
                                        onClick={() => toggleTag(tag)}
                                      >
                                        {tag}
                                      </Badge>
                                    ))}
                                  </div>
                                </div>
                              </PopoverContent>
                            </Popover>
                            
                            {selectedTags.length > 0 && (
                              <div className="flex flex-wrap gap-1 flex-1 overflow-x-auto pb-1" style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}>
                                {selectedTags.map(tag => (
                                  <Badge 
                                    key={tag}
                                    variant="default"
                                    className="bg-indigo-500 hover:bg-indigo-600 gap-1 truncate max-w-[150px]"
                                  >
                                    {tag}
                                    <X 
                                      className="h-3 w-3 cursor-pointer" 
                                      onClick={() => toggleTag(tag)}
                                    />
                                  </Badge>
                                ))}
                              </div>
                            )}
                          </div>
                        </div>
                        
                        {/* Results summary */}
                        <div className="text-sm text-muted-foreground dark:text-white/60 flex items-center justify-between">
                          <span>Showing {filteredPublicGroups.length} of {publicGroups.length} groups</span>
                          <span className="text-xs">Sorted by study hours</span>
                        </div>
                      </div>
                      
                      {/* Groups grid */}
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {filteredPublicGroups.length > 0 ? (
                          filteredPublicGroups.map((group: PublicGroup) => (
                            // Theme-aware group card
                            <div
                              key={group.id}
                              className={`flex flex-col p-6 bg-card dark:bg-gradient-to-br dark:from-slate-800/80 dark:to-slate-900/80 hover:bg-accent dark:hover:from-slate-700/80 dark:hover:to-slate-800/80 rounded-xl border border-border dark:border-white/10 transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-[1.02] group overflow-hidden relative`}
                            >
                              {/* Activity indicator */}
                              <div className={`absolute top-0 left-0 right-0 h-1 ${
                                group.activityLevel === 'high' 
                                  ? 'bg-gradient-to-r from-green-500 to-emerald-500' 
                                  : group.activityLevel === 'medium'
                                    ? 'bg-gradient-to-r from-amber-500 to-orange-500'
                                    : 'bg-gradient-to-r from-slate-400 to-slate-500'
                              }`}></div>
                              
                              <div className="flex items-center justify-between mb-3">
                                {/* Theme-aware text */}
                                <h3 className="text-lg font-medium text-card-foreground dark:group-hover:text-white transition-colors">{group.name}</h3>
                                {/* Keep badge style */}
                                <div className="text-xs bg-indigo-500/20 text-indigo-400 px-2 py-0.5 rounded-full flex items-center gap-1">
                                  <Globe className="h-3 w-3" />
                                  <span>Public</span>
                                </div>
                              </div>

                              {/* Theme-aware text */}
                              <p className="text-sm text-muted-foreground dark:text-white/70 mb-3 line-clamp-2">{group.description}</p>

                              {/* Study time highlight */}
                              <div className="flex items-center text-sm mb-3 bg-background/50 dark:bg-white/5 p-2 rounded-lg border border-border/50 dark:border-white/10">
                                <Clock className={`h-4 w-4 mr-2 ${
                                  group.activityLevel === 'high' 
                                    ? 'text-emerald-500' 
                                    : group.activityLevel === 'medium'
                                      ? 'text-amber-500'
                                      : 'text-slate-400'
                                }`} />
                                <span className="text-foreground dark:text-white/90">{formatGroupStudyTime(group.totalStudyTime)} total study time</span>
                              </div>

                              <div className="flex flex-wrap gap-2 mb-3">
                                {/* Theme-aware tags */}
                                {group.tags && group.tags.slice(0, 3).map((tag: string, index: number) => (
                                  <span 
                                    key={index} 
                                    className="text-xs bg-secondary dark:bg-white/10 text-secondary-foreground dark:text-white/80 px-2 py-0.5 rounded-full cursor-pointer hover:bg-secondary/70 dark:hover:bg-white/20"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      if (!selectedTags.includes(tag)) {
                                        toggleTag(tag);
                                      }
                                    }}
                                  >
                                    {tag}
                                  </span>
                                ))}
                                {group.tags && group.tags.length > 3 && (
                                  <span className="text-xs bg-secondary/50 dark:bg-white/5 text-secondary-foreground dark:text-white/60 px-2 py-0.5 rounded-full">
                                    +{group.tags.length - 3} more
                                  </span>
                                )}
                              </div>

                              {/* Theme-aware text */}
                              <div className="flex items-center justify-between text-sm text-muted-foreground dark:text-white/60 mb-4">
                                <div className="flex items-center gap-1">
                                  <Users className="h-4 w-4" />
                                  <span>{group.memberCount} members</span>
                                </div>
                                {/* Theme-aware text */}
                                <div className="text-xs text-muted-foreground/80 dark:text-white/50">
                                  Created {group.createdAt ? new Date(group.createdAt).toLocaleDateString() : 'recently'}
                                </div>
                              </div>

                              <div className="mt-auto pt-4">
                                {group.isUserMember ? (
                                  <Button
                                    className="w-full bg-emerald-600/50 hover:bg-emerald-600/70 text-white cursor-default"
                                    disabled
                                  >
                                    <Users className="h-4 w-4 mr-2" />
                                    Already a Member
                                  </Button>
                                ) : (
                                  <Button
                                    className="w-full bg-gradient-to-r from-indigo-600 to-indigo-700 hover:from-indigo-500 hover:to-indigo-600 transition-all duration-300"
                                    onClick={() => handleJoinPublicGroup(group.id)}
                                    disabled={joiningGroupId === group.id}
                                  >
                                    {joiningGroupId === group.id ? (
                                      <div className="flex items-center gap-2">
                                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current" />
                                        <span>Joining...</span>
                                      </div>
                                    ) : (
                                      <>Join Group</>
                                    )}
                                  </Button>
                                )}
                              </div>
                            </div>
                          ))
                        ) : (
                          <div className="col-span-full flex flex-col items-center justify-center py-12 text-center animate-fadeIn">
                            <div className="w-16 h-16 rounded-full bg-secondary/50 dark:bg-white/5 flex items-center justify-center mb-4">
                              <Search className="w-8 h-8 text-muted-foreground dark:text-white/40" />
                            </div>
                            <h3 className="text-xl font-medium text-foreground dark:text-white mb-2">No matching groups found</h3>
                            <p className="text-muted-foreground dark:text-white/60 max-w-md mb-6">
                              Try adjusting your search terms or filters to find more groups.
                            </p>
                            <Button
                              variant="outline"
                              onClick={clearFilters}
                              className="bg-secondary/50 dark:bg-white/5 hover:bg-secondary dark:hover:bg-white/10 border-border dark:border-white/10"
                            >
                              <X className="h-4 w-4 mr-2" />
                              Clear All Filters
                            </Button>
                          </div>
                        )}
                      </div>

                      <div className="flex justify-center mt-8">
                        {/* Theme-aware text */}
                        <p className="text-muted-foreground dark:text-white/60">
                          Looking for a private group?
                          <Button
                            variant="link"
                            className="text-indigo-400 hover:text-indigo-300 p-0 h-auto ml-1"
                            onClick={() => setIsJoinOpen(true)}
                          >
                            Join with invite code or link
                          </Button>
                        </p>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="joinCreate" data-tab="joinCreate">
              {/* Enhanced Join/Create Card */}
              <Card className="bg-white/5 dark:bg-gradient-to-br dark:from-slate-800/40 dark:to-slate-900/40 border border-white/20 dark:border-white/10 backdrop-blur-2xl rounded-3xl shadow-2xl overflow-hidden">
                <CardHeader className="border-b border-white/10 dark:border-white/10 pb-6 bg-gradient-to-r from-emerald-500/10 to-teal-500/10">
                  <CardTitle className="text-xl font-semibold flex items-center gap-3 text-card-foreground">
                    <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-emerald-500/20 to-teal-500/20 flex items-center justify-center">
                      <Settings2 className="h-5 w-5 text-emerald-400" />
                    </div>
                    <span className="bg-gradient-to-r from-emerald-400 to-teal-400 bg-clip-text text-transparent">Join or Create a New Group</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-8">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-10">
                    {/* Enhanced Join Group Section */}
                    <div className="relative bg-gradient-to-br from-indigo-500/5 to-purple-500/5 rounded-2xl p-8 border border-indigo-500/20 backdrop-blur-sm overflow-hidden">
                      <div className="absolute -top-10 -right-10 w-32 h-32 bg-gradient-to-br from-indigo-500/10 to-purple-500/10 rounded-full blur-2xl"></div>

                      <div className="relative z-10">
                        <div className="flex items-center gap-3 mb-6">
                          <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-indigo-500/20 to-purple-500/20 flex items-center justify-center">
                            <UserPlus className="h-6 w-6 text-indigo-400" />
                          </div>
                          <h3 className="text-2xl font-semibold bg-gradient-to-r from-indigo-400 to-purple-400 bg-clip-text text-transparent">
                            Join a Group
                          </h3>
                        </div>
                        <p className="text-muted-foreground dark:text-white/60 mb-8 leading-relaxed">
                          Join an existing study group using an invite code or browse public groups to find your perfect study community.
                        </p>

                        <div className="space-y-4">
                          <Button
                            variant="outline"
                            className="w-full bg-white/5 dark:bg-white/5 hover:bg-white/10 dark:hover:bg-white/10 border-white/20 dark:border-white/20 text-foreground dark:text-white backdrop-blur-sm py-3 text-base"
                            onClick={() => setIsJoinOpen(true)}
                          >
                            <Lock className="h-5 w-5 mr-2" />
                            Join with Invite Code or Link
                          </Button>

                          <Button
                            className="w-full bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 shadow-lg shadow-indigo-500/25 py-3 text-base"
                            onClick={() => {
                              console.log('Setting activeTab to discover');
                              setActiveTab("discover");
                            }}
                          >
                            <Globe className="h-5 w-5 mr-2" />
                            Browse Public Groups
                          </Button>
                        </div>
                      </div>
                    </div>

                    {/* Enhanced Create Group Section */}
                    <div className="relative bg-gradient-to-br from-pink-500/5 to-rose-500/5 rounded-2xl p-8 border border-pink-500/20 backdrop-blur-sm overflow-hidden">
                      <div className="absolute -top-10 -left-10 w-32 h-32 bg-gradient-to-br from-pink-500/10 to-rose-500/10 rounded-full blur-2xl"></div>

                      <div className="relative z-10">
                        <div className="flex items-center gap-3 mb-6">
                          <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-pink-500/20 to-rose-500/20 flex items-center justify-center">
                            <PlusCircle className="h-6 w-6 text-pink-400" />
                          </div>
                          <h3 className="text-2xl font-semibold bg-gradient-to-r from-pink-400 to-rose-400 bg-clip-text text-transparent">
                            Create a Group
                          </h3>
                        </div>
                        <p className="text-muted-foreground dark:text-white/60 mb-8 leading-relaxed">
                          Start your own study group and invite friends to collaborate and learn together in a focused environment.
                        </p>

                        <div className="space-y-6">
                          <div className="grid grid-cols-2 gap-4">
                            <div className="bg-gradient-to-br from-indigo-500/10 to-purple-500/10 border border-indigo-500/20 rounded-xl p-4 flex flex-col items-center text-center backdrop-blur-sm">
                              <div className="w-10 h-10 rounded-lg bg-gradient-to-br from-indigo-500/20 to-purple-500/20 flex items-center justify-center mb-3">
                                <Globe className="h-5 w-5 text-indigo-400" />
                              </div>
                              <h4 className="text-sm font-semibold text-foreground dark:text-white mb-1">Public Group</h4>
                              <p className="text-muted-foreground dark:text-white/60 text-xs leading-relaxed">
                                Anyone can discover and join
                              </p>
                            </div>

                            <div className="bg-gradient-to-br from-slate-500/10 to-slate-600/10 border border-slate-500/20 rounded-xl p-4 flex flex-col items-center text-center backdrop-blur-sm">
                              <div className="w-10 h-10 rounded-lg bg-gradient-to-br from-slate-500/20 to-slate-600/20 flex items-center justify-center mb-3">
                                <Lock className="h-5 w-5 text-slate-400" />
                              </div>
                              <h4 className="text-sm font-semibold text-foreground dark:text-white mb-1">Private Group</h4>
                              <p className="text-muted-foreground dark:text-white/60 text-xs leading-relaxed">
                                Invite-only access
                              </p>
                            </div>
                          </div>

                          <Button
                            className="w-full bg-gradient-to-r from-pink-500 to-rose-600 hover:from-pink-600 hover:to-rose-700 shadow-lg shadow-pink-500/25 py-3 text-base"
                            onClick={() => setIsCreateOpen(true)}
                          >
                            <Sparkles className="h-5 w-5 mr-2" />
                            Create New Group
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>

      {/* Enhanced Footer */}
      <footer className="relative z-10 mt-16 border-t border-white/10 dark:border-indigo-500/20 bg-gradient-to-b from-white/5 to-white/10 dark:from-slate-900/40 dark:to-slate-950/60 backdrop-blur-2xl">
        <div className="container mx-auto px-4 md:px-6 py-16">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
            <div className="flex flex-col space-y-6">
              <div className="flex items-center space-x-4">
                <div className="relative">
                  <img src="/icon-192x192.png" alt="IsotopeAI Logo" className="w-12 h-12 rounded-xl shadow-xl shadow-indigo-500/30" />
                  <div className="absolute inset-0 rounded-xl bg-gradient-to-br from-indigo-500/20 to-purple-500/20 blur-xl"></div>
                </div>
                <span className="text-xl font-bold bg-gradient-to-r from-indigo-400 via-purple-400 to-pink-400 bg-clip-text text-transparent">
                  IsotopeAI
                </span>
              </div>
              <p className="text-muted-foreground dark:text-white/60 max-w-md text-lg leading-relaxed">
                Your all-in-one platform for AI-powered learning, productivity tools, and collaborative study groups.
              </p>
            </div>

            <div className="flex flex-col md:items-end space-y-6">
              <div className="flex flex-wrap items-center gap-6">
                <FeedbackWidget className="text-muted-foreground dark:text-white/60 hover:text-indigo-400 dark:hover:text-indigo-400 transition-all duration-300 hover:scale-105" />
                <div className="w-1 h-1 rounded-full bg-white/20"></div>
                <a href="https://isotopeai.featurebase.app/changelog" target="_blank" rel="noopener noreferrer" className="text-muted-foreground dark:text-white/60 hover:text-purple-400 dark:hover:text-purple-400 transition-all duration-300 hover:scale-105">
                  Changelog
                </a>
                <div className="w-1 h-1 rounded-full bg-white/20"></div>
                <a
                  href="mailto:<EMAIL>"
                  className="text-muted-foreground dark:text-white/60 hover:text-pink-400 dark:hover:text-pink-400 transition-all duration-300 hover:scale-105"
                >
                  Contact
                </a>
                <div className="w-1 h-1 rounded-full bg-white/20"></div>
                <a
                  href="https://www.instagram.com/isotope.ai/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-muted-foreground dark:text-white/60 hover:text-rose-400 dark:hover:text-rose-400 transition-all duration-300 hover:scale-105 flex items-center gap-2"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="18"
                    height="18"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="lucide lucide-instagram"
                  >
                    <rect width="20" height="20" x="2" y="2" rx="5" ry="5" />
                    <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z" />
                    <line x1="17.5" x2="17.51" y1="6.5" y2="6.5" />
                  </svg>
                </a>
                <div className="w-1 h-1 rounded-full bg-white/20"></div>
                <a
                  href="https://www.reddit.com/r/Isotope/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-muted-foreground dark:text-white/60 hover:text-orange-400 dark:hover:text-orange-400 transition-all duration-300 hover:scale-105 flex items-center gap-2"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="18"
                    height="18"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <circle cx="12" cy="12" r="10" />
                    <circle cx="12" cy="9" r="1" />
                    <circle cx="12" cy="15" r="1" />
                    <path d="M8.5 9a2 2 0 0 0-2 2v0c0 1.1.9 2 2 2" />
                    <path d="M15.5 9a2 2 0 0 1 2 2v0c0 1.1-.9 2-2 2" />
                    <path d="M7.5 13h9" />
                    <path d="M10 16v-3" />
                    <path d="M14 16v-3" />
                  </svg>
                </a>
              </div>
              <p className="text-muted-foreground dark:text-white/50 text-base">
                Built with <span className="text-pink-400 animate-pulse">❤️</span> by a fellow JEEtard
              </p>
            </div>
          </div>
        </div>

        {/* Enhanced overlay with depth */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent pointer-events-none dark:block hidden" />
        <div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-indigo-500/50 to-transparent"></div>
      </footer>

      <CreateGroup 
        open={isCreateOpen} 
        onOpenChange={setIsCreateOpen} 
        onCreateSuccess={(groupId) => {
          // After group is created, we'll select it to show details
          setSelectedGroupId(groupId);
          // Switch to My Groups tab
          setActiveTab("myGroups");
          // Show success toast
          toast({
            title: "Group Created",
            description: "Your new group has been created successfully.",
            duration: 3000
          });
        }}
      />
      <JoinGroup 
        open={isJoinOpen} 
        onOpenChange={setIsJoinOpen}
        onJoinSuccess={(groupId) => {
          // After joining group, we'll select it to show details
          setSelectedGroupId(groupId);
          // Switch to My Groups tab
          setActiveTab("myGroups");
          // Show success toast
          toast({
            title: "Group Joined",
            description: "You have successfully joined the group.",
            duration: 3000
          });
        }}
      />
    </div>
  )
}
